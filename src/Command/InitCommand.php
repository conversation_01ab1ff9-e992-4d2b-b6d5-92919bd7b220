<?php

namespace Flexiwind\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Yaml\Yaml;

class InitCommand extends Command
{
    protected static $defaultName = 'init';

    protected function configure()
    {
        $this
            ->setDescription('Initialize Flexiwind in the current project')
            ->addOption('new-laravel', 'nl', InputOption::VALUE_NONE, 'Create a new Laravel project then initialize')
            ->addOption('new-symfony', 'ns', InputOption::VALUE_NONE, 'Create a new Symfony project then initialize');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        // === Handle new project creation ===
        if ($input->getOption('new-laravel')) {
            $io->section('Création d’un nouveau projet Laravel...');
            exec("laravel new my-app", $out, $code);
            if ($code !== 0) {
                $io->error("Failed to create Laravel project.");
                return Command::FAILURE;
            }
            chdir("my-app");
        }

        if ($input->getOption('new-symfony')) {
            $io->section('Création d’un nouveau projet Symfony...');
            exec("symfony new my-app --webapp", $out, $code);
            if ($code !== 0) {
                $io->error("Failed to create Symfony project.");
                return Command::FAILURE;
            }
            chdir("my-app");
        }

        // === Detect framework ===
        $framework = 'custom';
        if (file_exists('artisan')) {
            $framework = 'laravel';
        } elseif (file_exists('bin/console')) {
            $framework = 'symfony';
        }

        $io->title("Flexiwind Initialization ($framework)");

        // === Ask questions ===
        $theme = $io->choice(
            'Which theme would you like to use?',
            ['flexiwind', 'water', 'earth', 'air'],
            'flexiwind'
        );

        $useAlpine = $io->confirm('Do you use AlpineJS?', true);

        $useFlexiwind = true;
        if ($framework === 'custom') {
            $useFlexiwind = $io->confirm('Do you want to use Flexiwind for styling?', true);
        }

        // === Create config files ===
        $configFile = getcwd() . '/flexiwind.yaml';
        $keysFile   = getcwd() . '/.flexiwind/keys.yaml';

        if (!is_dir('.flexiwind')) {
            mkdir('.flexiwind', 0777, true);
        }

        $config = [
            'framework' => $framework,
            'theme'     => $theme,
            'alpine'    => $useAlpine,
            'flexiwind' => $useFlexiwind,
            'sources'   => [
                [
                    'name' => '@flexiwind',
                    'url'  => 'https://registry.flexiwind.dev/{name}.json',
                ]
            ],
            'cache' => '.flexiwind',
        ];

        file_put_contents($configFile, Yaml::dump($config, 4, 2));
        file_put_contents($keysFile, Yaml::dump(['tokens' => []], 4, 2));

        // === Generate project-specific files ===
        if ($framework === 'laravel') {
            $this->generateLaravelFiles($io);
        } elseif ($useFlexiwind) {
            $this->generateCustomFiles($io);
        }

        $io->success("Flexiwind initialized successfully!");
        $io->text("→ Configuration: flexiwind.yaml\n→ Keys: .flexiwind/keys.yaml");

        return Command::SUCCESS;
    }

    private function generateLaravelFiles(SymfonyStyle $io): void
    {
        $io->section('Generating Laravel files...');

        $this->ensureDir('app/Flexiwind');
        file_put_contents('app/Flexiwind/Helpers.php', <<<PHP
        <?php

        namespace App\Flexiwind;

        class Helpers {
            public static function hello() {
                return "Hello from Flexiwind!";
            }
        }
        PHP);

        $this->ensureDir('resources/js');
        file_put_contents('resources/js/flexilla.js', "// Flexiwind JS bootstrap\n");

        $this->ensureDir('resources/css');
        file_put_contents('resources/css/flexiwind.css', "/* Flexiwind base styles */\n");
        file_put_contents('resources/css/buttons.css', "/* Flexiwind button styles */\n");
        file_put_contents('resources/css/utilities.css', "/* Flexiwind utility classes */\n");
    }

    private function generateCustomFiles(SymfonyStyle $io): void
    {
        $io->section('Generating custom files...');

        $this->ensureDir('flexiwind/js');
        $this->ensureDir('flexiwind/css');

        file_put_contents('flexiwind/helpers.php', "<?php\n\nfunction flexiwind_hello() { return 'Hello from Flexiwind!'; }\n");
        file_put_contents('flexiwind/js/flexilla.js', "// Flexiwind JS bootstrap\n");
        file_put_contents('flexiwind/css/flexiwind.css', "/* Flexiwind base styles */\n");
        file_put_contents('flexiwind/css/buttons.css', "/* Flexiwind button styles */\n");
        file_put_contents('flexiwind/css/utilities.css', "/* Flexiwind utility classes */\n");
    }

    private function ensureDir(string $dir): void
    {
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
    }
}
