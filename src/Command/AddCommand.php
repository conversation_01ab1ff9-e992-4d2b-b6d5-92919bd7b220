<?php

namespace Flexiwind\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Yaml\Yaml;
use Symfony\Component\HttpClient\HttpClient;

class AddCommand extends Command
{
    protected static $defaultName = 'add';

    protected function configure()
    {
        $this
            ->setDescription('Add a UI component to the project')
            ->addArgument('component', InputArgument::REQUIRED, 'Component name (e.g.: @flexiwind/button)');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $component = $input->getArgument('component');

        $configFile = getcwd() . '/flexiwind.yaml';
        if (!file_exists($configFile)) {
            $io->error('flexiwind.yaml introuvable. Lancez `php flexiwind init` d’abord.');
            return Command::FAILURE;
        }

        $config = Yaml::parseFile($configFile);

        // Split namespace + name
        if (!preg_match('/^(@[a-zA-Z0-9_-]+)\/([a-zA-Z0-9_-]+)$/', $component, $matches)) {
            $io->error('Invalid component name. Expected format: @source/name');
            return Command::FAILURE;
        }

        $sourceName = $matches[1]; // e.g.: @flexiwind
        $compName   = $matches[2]; // e.g.: button

        // Find source
        $source = null;
        foreach ($config['sources'] as $src) {
            if ($src['name'] === $sourceName) {
                $source = $src;
                break;
            }
        }

        if (!$source) {
            $io->error("Source $sourceName not found in flexiwind.yaml");
            return Command::FAILURE;
        }

        $url = str_replace('{name}', $compName, $source['url']);
        $io->text("📥 Downloading component from $url ...");

        $client = HttpClient::create();
        try {
            $response = $client->request('GET', $url);
            $registry = $response->toArray();
        } catch (\Exception $e) {
            $io->error("Download error: " . $e->getMessage());
            return Command::FAILURE;
        }

        if (!isset($registry['files'])) {
            $io->error('Invalid registry JSON: missing `files` field.');
            return Command::FAILURE;
        }

        foreach ($registry['files'] as $file) {
            $from = $file['from'];
            $to   = getcwd() . '/' . $file['to'];

            // Create directory if necessary
            $dir = dirname($to);
            if (!is_dir($dir)) {
                mkdir($dir, 0777, true);
            }

            // MVP: just write a placeholder (later → real download)
            file_put_contents($to, "<!-- Component {$compName} installed from {$from} -->");

            $io->text("✅ File installed: {$file['to']}");
        }

        $io->success("Component {$component} installed!");
        return Command::SUCCESS;
    }
}
