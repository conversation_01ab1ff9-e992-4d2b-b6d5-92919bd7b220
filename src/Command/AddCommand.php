<?php

namespace Flexiwind\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Yaml\Yaml;
use Symfony\Component\HttpClient\HttpClient;

class AddCommand extends Command
{
    protected static $defaultName = 'add';

    protected function configure()
    {
        $this
            ->setDescription('Ajoute un composant UI au projet')
            ->addArgument('component', InputArgument::REQUIRED, 'Nom du composant (ex: @flexiwind/button)');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $component = $input->getArgument('component');

        $configFile = getcwd() . '/flexiwind.yaml';
        if (!file_exists($configFile)) {
            $io->error('flexiwind.yaml introuvable. Lancez `php flexiwind init` d’abord.');
            return Command::FAILURE;
        }

        $config = Yaml::parseFile($configFile);

        // Découper namespace + nom
        if (!preg_match('/^(@[a-zA-Z0-9_-]+)\/([a-zA-Z0-9_-]+)$/', $component, $matches)) {
            $io->error('Nom de composant invalide. Format attendu : @source/name');
            return Command::FAILURE;
        }

        $sourceName = $matches[1]; // ex: @flexiwind
        $compName   = $matches[2]; // ex: button

        // Trouver source
        $source = null;
        foreach ($config['sources'] as $src) {
            if ($src['name'] === $sourceName) {
                $source = $src;
                break;
            }
        }

        if (!$source) {
            $io->error("Source $sourceName non trouvée dans flexiwind.yaml");
            return Command::FAILURE;
        }

        $url = str_replace('{name}', $compName, $source['url']);
        $io->text("📥 Téléchargement du composant depuis $url ...");

        $client = HttpClient::create();
        try {
            $response = $client->request('GET', $url);
            $registry = $response->toArray();
        } catch (\Exception $e) {
            $io->error("Erreur lors du téléchargement : " . $e->getMessage());
            return Command::FAILURE;
        }

        if (!isset($registry['files'])) {
            $io->error('Registry JSON invalide : champ `files` manquant.');
            return Command::FAILURE;
        }

        foreach ($registry['files'] as $file) {
            $from = $file['from'];
            $to   = getcwd() . '/' . $file['to'];

            // Créer dossier si nécessaire
            $dir = dirname($to);
            if (!is_dir($dir)) {
                mkdir($dir, 0777, true);
            }

            // Ici MVP : juste écrire un placeholder (plus tard → téléchargement réel)
            file_put_contents($to, "<!-- Composant {$compName} installé depuis {$from} -->");

            $io->text("✅ Fichier installé : {$file['to']}");
        }

        $io->success("Composant {$component} installé !");
        return Command::SUCCESS;
    }
}
